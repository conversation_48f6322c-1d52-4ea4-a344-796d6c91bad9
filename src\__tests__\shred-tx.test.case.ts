export const txs = [
  {
    skipTest: true,
    filters: ['pumpfun'],
    transaction: {
      transaction: {
        signatures: [
          {
            type: 'Buffer',
            data: [
              193, 14, 223, 182, 96, 112, 88, 237, 147, 253, 230, 15, 136, 61, 23, 247, 138, 250, 236, 86, 26, 180, 169,
              229, 98, 241, 66, 56, 101, 57, 95, 81, 11, 52, 69, 60, 231, 219, 97, 34, 93, 159, 103, 225, 70, 14, 1,
              223, 18, 36, 209, 31, 87, 111, 19, 229, 103, 214, 223, 63, 1, 75, 188, 5,
            ],
          },
        ],
        message: {
          header: {
            numRequiredSignatures: 1,
            numReadonlySignedAccounts: 0,
            numReadonlyUnsignedAccounts: 3,
          },
          accountKeys: [
            {
              type: 'Buffer',
              data: [
                210, 25, 160, 91, 47, 19, 2, 43, 68, 206, 218, 219, 178, 49, 132, 230, 171, 91, 116, 19, 26, 26, 156,
                114, 177, 206, 133, 245, 242, 69, 220, 24,
              ],
            },
            {
              type: 'Buffer',
              data: [
                45, 150, 223, 14, 242, 20, 246, 6, 171, 58, 170, 123, 226, 80, 31, 219, 238, 227, 82, 14, 42, 179, 199,
                109, 66, 74, 88, 131, 61, 143, 156, 168,
              ],
            },
            {
              type: 'Buffer',
              data: [
                138, 34, 157, 5, 173, 212, 162, 136, 139, 75, 93, 164, 38, 254, 255, 247, 80, 32, 10, 76, 123, 139, 221,
                208, 171, 25, 20, 60, 28, 148, 141, 4,
              ],
            },
            {
              type: 'Buffer',
              data: [
                157, 33, 194, 233, 80, 240, 162, 171, 14, 38, 147, 248, 115, 45, 205, 144, 232, 235, 20, 47, 71, 64,
                155, 77, 207, 215, 53, 97, 55, 80, 71, 91,
              ],
            },
            {
              type: 'Buffer',
              data: [
                196, 150, 12, 158, 231, 86, 242, 4, 10, 52, 52, 153, 32, 170, 59, 56, 180, 136, 13, 59, 152, 62, 104,
                175, 162, 8, 185, 120, 65, 202, 84, 248,
              ],
            },
            {
              type: 'Buffer',
              data: [
                3, 6, 70, 111, 229, 33, 23, 50, 255, 236, 173, 186, 114, 195, 155, 231, 188, 140, 229, 187, 197, 247,
                18, 107, 44, 67, 155, 58, 64, 0, 0, 0,
              ],
            },
            {
              type: 'Buffer',
              data: [
                181, 227, 74, 20, 226, 188, 115, 72, 105, 14, 225, 245, 175, 93, 238, 214, 85, 56, 64, 163, 109, 170,
                184, 96, 176, 80, 96, 115, 189, 192, 60, 16,
              ],
            },
            {
              type: 'Buffer',
              data: [
                10, 241, 195, 67, 33, 136, 202, 58, 99, 81, 53, 161, 58, 24, 149, 26, 206, 189, 41, 230, 172, 46, 237,
                56, 249, 30, 109, 209, 178, 59, 60, 87,
              ],
            },
          ],
          recentBlockhash: {
            type: 'Buffer',
            data: [
              200, 28, 253, 253, 188, 161, 247, 79, 157, 28, 4, 32, 4, 42, 203, 30, 97, 156, 188, 158, 148, 184, 211,
              235, 119, 115, 198, 29, 6, 250, 146, 130,
            ],
          },
          instructions: [
            {
              programIdIndex: 5,
              accounts: {
                type: 'Buffer',
                data: [7],
              },
              data: {
                type: 'Buffer',
                data: [3, 93, 114, 0, 0, 0, 0, 0, 0],
              },
            },
            {
              programIdIndex: 5,
              accounts: {},
              data: {
                type: 'Buffer',
                data: [2, 8, 75, 6, 0],
              },
            },
            {
              programIdIndex: 6,
              accounts: {
                type: 'Buffer',
                data: [0, 1, 33, 34, 28, 25],
              },
              data: {
                type: 'Buffer',
                data: [47, 62, 155, 172, 131, 205, 37, 201, 160, 134, 1, 0, 0, 0, 0, 0],
              },
            },
            {
              programIdIndex: 6,
              accounts: {
                type: 'Buffer',
                data: [
                  34, 28, 25, 0, 26, 6, 30, 0, 23, 22, 21, 1, 3, 18, 19, 34, 34, 33, 27, 20, 8, 32, 10, 11, 3, 4, 31,
                  27, 9, 32, 0, 34, 34, 24, 32, 12, 17, 32, 16, 14, 4, 2, 31, 29, 15, 32, 0, 34, 34, 24, 32, 13,
                ],
              },
              data: {
                type: 'Buffer',
                data: [
                  248, 198, 158, 145, 225, 117, 135, 200, 3, 0, 0, 0, 10, 160, 134, 1, 0, 0, 0, 0, 0, 0, 2, 4, 150, 56,
                  0, 0, 0, 0, 0, 0, 1, 1, 4, 44, 229, 20, 0, 0, 0, 0, 0, 1, 2, 138, 59, 0, 0, 0, 0, 0, 0, 208, 7, 0, 0,
                ],
              },
            },
          ],
          versioned: true,
          addressTableLookups: [
            {
              accountKey: {
                type: 'Buffer',
                data: [
                  20, 174, 116, 78, 241, 148, 105, 193, 228, 236, 181, 126, 228, 97, 166, 34, 217, 49, 27, 40, 135, 209,
                  79, 212, 22, 121, 147, 17, 236, 114, 126, 212,
                ],
              },
              writableIndexes: {
                type: 'Buffer',
                data: [159, 162, 165, 163, 158],
              },
              readonlyIndexes: {},
            },
            {
              accountKey: {
                type: 'Buffer',
                data: [
                  42, 37, 98, 81, 219, 132, 0, 108, 13, 111, 248, 140, 140, 231, 22, 213, 197, 201, 10, 224, 13, 60,
                  199, 255, 12, 172, 198, 64, 119, 189, 47, 81,
                ],
              },
              writableIndexes: {
                type: 'Buffer',
                data: [210, 202, 209, 201, 211],
              },
              readonlyIndexes: {},
            },
            {
              accountKey: {
                type: 'Buffer',
                data: [
                  62, 59, 146, 104, 93, 141, 46, 175, 133, 65, 210, 191, 66, 220, 212, 147, 172, 208, 191, 210, 55, 161,
                  123, 64, 13, 144, 183, 131, 55, 129, 241, 11,
                ],
              },
              writableIndexes: {
                type: 'Buffer',
                data: [64, 233, 232, 235],
              },
              readonlyIndexes: {
                type: 'Buffer',
                data: [230, 234, 6],
              },
            },
            {
              accountKey: {
                type: 'Buffer',
                data: [
                  232, 171, 89, 33, 31, 45, 194, 173, 127, 94, 133, 107, 1, 180, 189, 254, 96, 157, 109, 82, 160, 121,
                  40, 149, 197, 36, 223, 83, 28, 25, 181, 139,
                ],
              },
              writableIndexes: {},
              readonlyIndexes: {
                type: 'Buffer',
                data: [0, 17, 56, 6, 33, 212, 57, 25, 32, 4],
              },
            },
          ],
        },
      },
      slot: '*********',
    },
    createdAt: '2025-06-07T10:33:28.919Z',
  },
  {
    skipTest: false,
    filters: ['pumpfun'],
    transaction: {
      transaction: {
        signatures: [
          {
            type: 'Buffer',
            data: [
              113, 184, 89, 78, 46, 195, 234, 178, 239, 23, 212, 48, 118, 174, 49, 34, 62, 36, 246, 190, 57, 108, 167,
              55, 47, 34, 159, 143, 206, 223, 51, 97, 26, 13, 9, 17, 149, 59, 239, 7, 223, 255, 178, 0, 8, 245, 188, 31,
              127, 102, 241, 91, 126, 86, 115, 241, 135, 70, 67, 118, 4, 245, 144, 0,
            ],
          },
        ],
        message: {
          header: {
            numRequiredSignatures: 1,
            numReadonlySignedAccounts: 0,
            numReadonlyUnsignedAccounts: 8,
          },
          accountKeys: [
            {
              type: 'Buffer',
              data: [
                210, 25, 160, 91, 47, 19, 2, 43, 68, 206, 218, 219, 178, 49, 132, 230, 171, 91, 116, 19, 26, 26, 156,
                114, 177, 206, 133, 245, 242, 69, 220, 24,
              ],
            },
            {
              type: 'Buffer',
              data: [
                31, 94, 57, 41, 107, 222, 177, 187, 21, 235, 86, 164, 179, 11, 43, 203, 15, 107, 6, 231, 107, 249, 4,
                99, 125, 208, 76, 30, 107, 247, 208, 25,
              ],
            },
            {
              type: 'Buffer',
              data: [
                45, 150, 223, 14, 242, 20, 246, 6, 171, 58, 170, 123, 226, 80, 31, 219, 238, 227, 82, 14, 42, 179, 199,
                109, 66, 74, 88, 131, 61, 143, 156, 168,
              ],
            },
            {
              type: 'Buffer',
              data: [
                61, 157, 249, 37, 194, 29, 70, 153, 195, 19, 154, 31, 234, 143, 133, 141, 255, 138, 86, 103, 220, 50,
                190, 2, 152, 230, 61, 136, 237, 55, 255, 156,
              ],
            },
            {
              type: 'Buffer',
              data: [
                102, 25, 16, 181, 177, 185, 177, 238, 58, 218, 162, 147, 143, 249, 245, 217, 101, 208, 86, 204, 144,
                227, 111, 165, 212, 93, 83, 199, 205, 67, 6, 112,
              ],
            },
            {
              type: 'Buffer',
              data: [
                138, 34, 157, 5, 173, 212, 162, 136, 139, 75, 93, 164, 38, 254, 255, 247, 80, 32, 10, 76, 123, 139, 221,
                208, 171, 25, 20, 60, 28, 148, 141, 4,
              ],
            },
            {
              type: 'Buffer',
              data: [
                217, 115, 139, 221, 138, 172, 251, 169, 221, 78, 96, 48, 251, 7, 164, 46, 197, 139, 151, 233, 55, 167,
                196, 239, 238, 89, 228, 169, 75, 145, 67, 207,
              ],
            },
            {
              type: 'Buffer',
              data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            },
            {
              type: 'Buffer',
              data: [
                93, 66, 123, 56, 92, 64, 138, 13, 215, 27, 39, 209, 56, 185, 31, 130, 10, 136, 11, 189, 211, 255, 204,
                107, 240, 194, 105, 125, 9, 167, 219, 94,
              ],
            },
            {
              type: 'Buffer',
              data: [
                140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16,
                132, 4, 142, 123, 216, 219, 233, 248, 89,
              ],
            },
            {
              type: 'Buffer',
              data: [
                3, 6, 70, 111, 229, 33, 23, 50, 255, 236, 173, 186, 114, 195, 155, 231, 188, 140, 229, 187, 197, 247,
                18, 107, 44, 67, 155, 58, 64, 0, 0, 0,
              ],
            },
            {
              type: 'Buffer',
              data: [
                2, 237, 82, 176, 120, 114, 68, 200, 192, 57, 175, 72, 205, 225, 116, 187, 197, 245, 171, 88, 204, 159,
                131, 121, 221, 217, 24, 237, 97, 255, 213, 32,
              ],
            },
            {
              type: 'Buffer',
              data: [
                198, 250, 122, 243, 190, 219, 173, 58, 61, 101, 243, 106, 171, 201, 116, 49, 177, 187, 228, 194, 210,
                246, 224, 228, 124, 166, 2, 3, 69, 47, 93, 97,
              ],
            },
            {
              type: 'Buffer',
              data: [
                6, 155, 136, 87, 254, 171, 129, 132, 251, 104, 127, 99, 70, 24, 192, 53, 218, 196, 57, 220, 26, 235, 59,
                85, 152, 160, 240, 0, 0, 0, 0, 1,
              ],
            },
            {
              type: 'Buffer',
              data: [
                6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91,
                55, 145, 58, 140, 245, 133, 126, 255, 0, 169,
              ],
            },
          ],
          recentBlockhash: {
            type: 'Buffer',
            data: [
              32, 99, 236, 253, 137, 24, 59, 114, 100, 211, 39, 244, 236, 26, 46, 222, 76, 120, 210, 161, 117, 213, 250,
              79, 218, 162, 135, 141, 13, 119, 114, 123,
            ],
          },
          instructions: [
            {
              programIdIndex: 10,
              accounts: {},
              data: {
                type: 'Buffer',
                data: [2, 64, 13, 3, 0],
              },
            },
            {
              programIdIndex: 10,
              accounts: {},
              data: {
                type: 'Buffer',
                data: [3, 32, 161, 7, 0, 0, 0, 0, 0],
              },
            },
            {
              programIdIndex: 9,
              accounts: {
                type: 'Buffer',
                data: [0, 5, 0, 12, 7, 14],
              },
              data: {
                type: 'Buffer',
                data: [1],
              },
            },
            {
              programIdIndex: 9,
              accounts: {
                type: 'Buffer',
                data: [0, 2, 0, 13, 7, 14],
              },
              data: {
                type: 'Buffer',
                data: [1],
              },
            },
            {
              programIdIndex: 7,
              accounts: {
                type: 'Buffer',
                data: [0, 2],
              },
              data: {
                type: 'Buffer',
                data: [2, 0, 0, 0, 160, 134, 1, 0, 0, 0, 0, 0],
              },
            },
            {
              programIdIndex: 14,
              accounts: {
                type: 'Buffer',
                data: [2],
              },
              data: {
                type: 'Buffer',
                data: [17],
              },
            },
            {
              programIdIndex: 11,
              accounts: {
                type: 'Buffer',
                data: [0, 0, 13, 12, 8, 1, 2, 6, 5, 3, 4, 7, 14, 9],
              },
              data: {
                type: 'Buffer',
                data: [
                  178, 144, 26, 216, 241, 187, 206, 130, 19, 32, 0, 0, 0, 0, 0, 0, 0, 13, 210, 19, 125, 248, 160, 255,
                  255, 255, 255, 255, 255, 255, 0, 39, 235, 142, 36, 214, 153, 108, 0, 0, 160, 134, 1, 0, 0, 0, 0, 0,
                  97, 59, 0, 0, 0, 0, 0, 0, 1, 22, 68, 104, 0, 0, 0, 0, 235, 225, 15, 7, 168, 132, 84, 212, 51, 51, 92,
                  110, 54, 96, 136, 60, 248, 26, 165, 56, 182, 151, 45, 35, 151, 139, 131, 180, 132, 97, 144, 114, 77,
                  227, 135, 115, 26, 11, 27, 66, 20, 69, 118, 17, 159, 79, 102, 168, 39, 67, 30, 66, 194, 69, 68, 14,
                  149, 97, 56, 171, 109, 176, 71, 83, 1, 0, 0, 0, 0, 0, 0, 0, 0, 160, 134, 1, 0, 0, 0, 0, 0,
                ],
              },
            },
            {
              programIdIndex: 14,
              accounts: {
                type: 'Buffer',
                data: [2, 0, 0],
              },
              data: {
                type: 'Buffer',
                data: [9],
              },
            },
          ],
          versioned: false,
          addressTableLookups: [],
        },
      },
      slot: '*********',
    },
    createdAt: '2025-06-07T10:35:17.730Z',
  },
  {
    skipTest: true,
    filters: ['pumpfun'],
    transaction: {
      transaction: {
        signatures: [
          {
            type: 'Buffer',
            data: [
              154, 167, 56, 118, 149, 127, 154, 70, 1, 238, 48, 247, 68, 253, 100, 229, 107, 206, 233, 16, 125, 152, 70,
              106, 85, 80, 160, 191, 225, 109, 164, 10, 232, 146, 43, 198, 8, 111, 217, 239, 1, 217, 178, 169, 22, 181,
              164, 171, 214, 76, 112, 171, 210, 59, 54, 50, 143, 243, 40, 34, 150, 178, 98, 3,
            ],
          },
        ],
        message: {
          header: {
            numRequiredSignatures: 1,
            numReadonlySignedAccounts: 0,
            numReadonlyUnsignedAccounts: 8,
          },
          accountKeys: [
            {
              type: 'Buffer',
              data: [
                210, 25, 160, 91, 47, 19, 2, 43, 68, 206, 218, 219, 178, 49, 132, 230, 171, 91, 116, 19, 26, 26, 156,
                114, 177, 206, 133, 245, 242, 69, 220, 24,
              ],
            },
            {
              type: 'Buffer',
              data: [
                36, 180, 160, 223, 180, 112, 75, 11, 136, 97, 5, 85, 168, 251, 224, 47, 113, 63, 157, 94, 168, 76, 143,
                38, 253, 177, 84, 167, 123, 116, 83, 75,
              ],
            },
            {
              type: 'Buffer',
              data: [
                70, 171, 40, 74, 104, 136, 214, 228, 4, 58, 250, 164, 43, 180, 123, 204, 117, 147, 183, 92, 6, 150, 63,
                119, 63, 231, 219, 39, 31, 127, 195, 9,
              ],
            },
            {
              type: 'Buffer',
              data: [
                138, 34, 157, 5, 173, 212, 162, 136, 139, 75, 93, 164, 38, 254, 255, 247, 80, 32, 10, 76, 123, 139, 221,
                208, 171, 25, 20, 60, 28, 148, 141, 4,
              ],
            },
            {
              type: 'Buffer',
              data: [
                206, 73, 33, 160, 155, 178, 173, 17, 114, 59, 205, 103, 169, 19, 18, 187, 29, 228, 85, 162, 31, 37, 234,
                145, 114, 195, 69, 89, 13, 108, 16, 182,
              ],
            },
            {
              type: 'Buffer',
              data: [
                12, 120, 74, 5, 203, 184, 37, 238, 205, 157, 177, 218, 253, 143, 126, 58, 215, 180, 71, 18, 95, 241,
                194, 193, 239, 159, 80, 242, 246, 110, 23, 233,
              ],
            },
            {
              type: 'Buffer',
              data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            },
            {
              type: 'Buffer',
              data: [
                82, 97, 209, 74, 172, 197, 188, 14, 236, 99, 93, 168, 112, 90, 31, 112, 163, 158, 227, 90, 154, 207, 11,
                248, 242, 44, 198, 206, 73, 1, 157, 122,
              ],
            },
            {
              type: 'Buffer',
              data: [
                3, 6, 70, 111, 229, 33, 23, 50, 255, 236, 173, 186, 114, 195, 155, 231, 188, 140, 229, 187, 197, 247,
                18, 107, 44, 67, 155, 58, 64, 0, 0, 0,
              ],
            },
            {
              type: 'Buffer',
              data: [
                180, 63, 250, 39, 245, 215, 246, 74, 116, 192, 155, 31, 41, 88, 121, 222, 75, 9, 171, 54, 223, 201, 221,
                81, 75, 50, 26, 167, 179, 140, 229, 232,
              ],
            },
            {
              type: 'Buffer',
              data: [
                231, 74, 217, 108, 227, 101, 159, 211, 19, 81, 0, 40, 75, 247, 120, 4, 91, 133, 16, 168, 243, 78, 73,
                140, 146, 46, 238, 111, 195, 5, 248, 105,
              ],
            },
            {
              type: 'Buffer',
              data: [
                10, 241, 195, 67, 33, 136, 202, 58, 99, 81, 53, 161, 58, 24, 149, 26, 206, 189, 41, 230, 172, 46, 237,
                56, 249, 30, 109, 209, 178, 59, 60, 87,
              ],
            },
            {
              type: 'Buffer',
              data: [
                4, 121, 213, 91, 242, 49, 192, 110, 238, 116, 197, 110, 206, 104, 21, 7, 253, 177, 178, 222, 163, 244,
                142, 81, 2, 177, 205, 162, 86, 188, 19, 143,
              ],
            },
            {
              type: 'Buffer',
              data: [
                6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91,
                55, 145, 58, 140, 245, 133, 126, 255, 0, 169,
              ],
            },
          ],
          recentBlockhash: {
            type: 'Buffer',
            data: [
              15, 135, 41, 180, 251, 149, 203, 97, 58, 41, 233, 161, 145, 218, 180, 60, 197, 31, 255, 14, 96, 114, 234,
              209, 78, 119, 68, 127, 235, 101, 215, 145,
            ],
          },
          instructions: [
            {
              programIdIndex: 8,
              accounts: {},
              data: {
                type: 'Buffer',
                data: [2, 113, 137, 2, 0],
              },
            },
            {
              programIdIndex: 8,
              accounts: {},
              data: {
                type: 'Buffer',
                data: [3, 120, 168, 2, 0, 0, 0, 0, 0],
              },
            },
            {
              programIdIndex: 12,
              accounts: {
                type: 'Buffer',
                data: [4, 0, 25, 13, 6],
              },
              data: {
                type: 'Buffer',
                data: [147, 241, 123, 100, 244, 132, 174, 118, 252],
              },
            },
            {
              programIdIndex: 12,
              accounts: {
                type: 'Buffer',
                data: [
                  13, 0, 3, 4, 12, 25, 12, 9, 12, 22, 13, 0, 14, 4, 15, 3, 16, 1, 5, 2, 21, 24, 19, 10, 7, 17, 18, 4, 3,
                  20, 23, 26, 0, 13, 11,
                ],
              },
              data: {
                type: 'Buffer',
                data: [
                  229, 23, 203, 151, 122, 227, 173, 42, 2, 0, 0, 0, 17, 0, 65, 0, 2, 58, 0, 35, 0, 2, 87, 59, 0, 0, 0,
                  0, 0, 0, 171, 134, 1, 0, 0, 0, 0, 0, 208, 7, 0,
                ],
              },
            },
            {
              programIdIndex: 13,
              accounts: {
                type: 'Buffer',
                data: [4, 0, 0],
              },
              data: {
                type: 'Buffer',
                data: [9],
              },
            },
          ],
          versioned: true,
          addressTableLookups: [
            {
              accountKey: {
                type: 'Buffer',
                data: [
                  121, 101, 18, 115, 246, 165, 46, 55, 11, 137, 133, 173, 181, 110, 196, 150, 120, 224, 192, 165, 7, 6,
                  43, 159, 4, 146, 93, 148, 17, 184, 85, 80,
                ],
              },
              writableIndexes: {
                type: 'Buffer',
                data: [144, 105, 109],
              },
              readonlyIndexes: {
                type: 'Buffer',
                data: [108, 0],
              },
            },
            {
              accountKey: {
                type: 'Buffer',
                data: [
                  243, 115, 121, 214, 132, 181, 161, 116, 229, 153, 34, 225, 57, 106, 151, 46, 52, 23, 15, 209, 14, 0,
                  75, 243, 145, 168, 29, 103, 61, 186, 51, 240,
                ],
              },
              writableIndexes: {
                type: 'Buffer',
                data: [18, 13, 12, 11],
              },
              readonlyIndexes: {
                type: 'Buffer',
                data: [14, 15, 42, 16],
              },
            },
          ],
        },
      },
      slot: '*********',
    },
    createdAt: '2025-06-07T10:37:28.986Z',
  },
];
