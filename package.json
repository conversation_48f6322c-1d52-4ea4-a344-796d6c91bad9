{"name": "solana-dex-parser", "version": "2.5.7", "description": "Solana Dex Transaction Parser", "author": "cxcx", "license": "MIT", "repository": {"type": "git", "url": "github:cxcx-ai/solana-dex-parser"}, "keywords": ["solana", "dex", "parser", "swap", "transaction", "blockchain", "liquidity", "raydium", "raydiumv4", "raydium launchpad", "jupiter", "moonshot", "meteora", "orca", "pumpfun", "pumpswap", "boopfun", "okx"], "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*.js", "dist/**/*.d.ts", "dist/**/*.js.map", "!dist/**/*.test.*", "!dist/**/__tests__/**"], "scripts": {"build": "tsc", "pretest": "npm run build", "prepublishOnly": "npm run build", "test": "jest", "lint": "eslint src --ext .ts", "format": "prettier --write \"src/**/*.ts\""}, "dependencies": {"@solana/web3.js": "^1.87.0", "dotenv": "^16.4.7"}, "devDependencies": {"@types/bs58": "^4.0.4", "@types/jest": "^29.5.0", "@types/json-bigint": "^1.0.4", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "json-bigint": "^1.0.0", "prettier": "^3.0.0", "ts-jest": "^29.1.0", "typescript": "^5.0.0"}, "overrides": {"bs58": "4.0.1"}}