syntax = "proto3";

import public "solana-storage.proto";

option go_package = "github.com/rpcpool/yellowstone-grpc/examples/golang/proto";

package geyser;

service Geyser {
  rpc Subscribe(stream SubscribeRequest) returns (stream SubscribeUpdate) {}
  rpc Ping(PingRequest) returns (PongResponse) {}
  rpc GetLatestBlockhash(GetLatestBlockhashRequest) returns (GetLatestBlockhashResponse) {}
  rpc GetBlockHeight(GetBlockHeightRequest) returns (GetBlockHeightResponse) {}
  rpc GetSlot(GetSlotRequest) returns (GetSlotResponse) {}
  rpc IsBlockhashValid(IsBlockhashValidRequest) returns (IsBlockhashValidResponse) {}
  rpc GetVersion(GetVersionRequest) returns (GetVersionResponse) {}
}

enum CommitmentLevel {
  PROCESSED = 0;
  CONFIRMED = 1;
  FINALIZED = 2;
}

message SubscribeRequest {
  map<string, SubscribeRequestFilterAccounts> accounts = 1;
  map<string, SubscribeRequestFilterSlots> slots = 2;
  map<string, SubscribeRequestFilterTransactions> transactions = 3;
  map<string, SubscribeRequestFilterTransactions> transactions_status = 10;
  map<string, SubscribeRequestFilterBlocks> blocks = 4;
  map<string, SubscribeRequestFilterBlocksMeta> blocks_meta = 5;
  map<string, SubscribeRequestFilterEntry> entry = 8;
  optional CommitmentLevel commitment = 6;
  repeated SubscribeRequestAccountsDataSlice accounts_data_slice = 7;
  optional SubscribeRequestPing ping = 9;
}

message SubscribeRequestFilterAccounts {
  repeated string account = 2;
  repeated string owner = 3;
  repeated SubscribeRequestFilterAccountsFilter filters = 4;
}

message SubscribeRequestFilterAccountsFilter {
  oneof filter {
    SubscribeRequestFilterAccountsFilterMemcmp memcmp = 1;
    uint64 datasize = 2;
    bool token_account_state = 3;
  }
}

message SubscribeRequestFilterAccountsFilterMemcmp {
  uint64 offset = 1;
  oneof data {
    bytes bytes = 2;
    string base58 = 3;
    string base64 = 4;
  }
}

message SubscribeRequestFilterSlots {
  optional bool filter_by_commitment = 1;
}

message SubscribeRequestFilterTransactions {
  optional bool vote = 1;
  optional bool failed = 2;
  optional string signature = 5;
  repeated string account_include = 3;
  repeated string account_exclude = 4;
  repeated string account_required = 6;
}

message SubscribeRequestFilterBlocks {
  repeated string account_include = 1;
  optional bool include_transactions = 2;
  optional bool include_accounts = 3;
  optional bool include_entries = 4;
}

message SubscribeRequestFilterBlocksMeta {}

message SubscribeRequestFilterEntry {}

message SubscribeRequestAccountsDataSlice {
  uint64 offset = 1;
  uint64 length = 2;
}

message SubscribeRequestPing {
  int32 id = 1;
}

message SubscribeUpdate {
  repeated string filters = 1;
  oneof update_oneof {
    SubscribeUpdateAccount account = 2;
    SubscribeUpdateSlot slot = 3;
    SubscribeUpdateTransaction transaction = 4;
    SubscribeUpdateTransactionStatus transaction_status = 10;
    SubscribeUpdateBlock block = 5;
    SubscribeUpdatePing ping = 6;
    SubscribeUpdatePong pong = 9;
    SubscribeUpdateBlockMeta block_meta = 7;
    SubscribeUpdateEntry entry = 8;
  }
}

message SubscribeUpdateAccount {
  SubscribeUpdateAccountInfo account = 1;
  uint64 slot = 2;
  bool is_startup = 3;
}

message SubscribeUpdateAccountInfo {
  bytes pubkey = 1;
  uint64 lamports = 2;
  bytes owner = 3;
  bool executable = 4;
  uint64 rent_epoch = 5;
  bytes data = 6;
  uint64 write_version = 7;
  optional bytes txn_signature = 8;
}

message SubscribeUpdateSlot {
  uint64 slot = 1;
  optional uint64 parent = 2;
  CommitmentLevel status = 3;
}

message SubscribeUpdateTransaction {
  SubscribeUpdateTransactionInfo transaction = 1;
  uint64 slot = 2;
}

message SubscribeUpdateTransactionInfo {
  bytes signature = 1;
  bool is_vote = 2;
  solana.storage.ConfirmedBlock.Transaction transaction = 3;
  solana.storage.ConfirmedBlock.TransactionStatusMeta meta = 4;
  uint64 index = 5;
}

message SubscribeUpdateTransactionStatus {
  uint64 slot = 1;
  bytes signature = 2;
  bool is_vote = 3;
  uint64 index = 4;
  solana.storage.ConfirmedBlock.TransactionError err = 5;
}

message SubscribeUpdateBlock {
  uint64 slot = 1;
  string blockhash = 2;
  solana.storage.ConfirmedBlock.Rewards rewards = 3;
  solana.storage.ConfirmedBlock.UnixTimestamp block_time = 4;
  solana.storage.ConfirmedBlock.BlockHeight block_height = 5;
  uint64 parent_slot = 7;
  string parent_blockhash = 8;
  uint64 executed_transaction_count = 9;
  repeated SubscribeUpdateTransactionInfo transactions = 6;
  uint64 updated_account_count = 10;
  repeated SubscribeUpdateAccountInfo accounts = 11;
  uint64 entries_count = 12;
  repeated SubscribeUpdateEntry entries = 13;
}

message SubscribeUpdateBlockMeta {
  uint64 slot = 1;
  string blockhash = 2;
  solana.storage.ConfirmedBlock.Rewards rewards = 3;
  solana.storage.ConfirmedBlock.UnixTimestamp block_time = 4;
  solana.storage.ConfirmedBlock.BlockHeight block_height = 5;
  uint64 parent_slot = 6;
  string parent_blockhash = 7;
  uint64 executed_transaction_count = 8;
  uint64 entries_count = 9;
}

message SubscribeUpdateEntry {
  uint64 slot = 1;
  uint64 index = 2;
  uint64 num_hashes = 3;
  bytes hash = 4;
  uint64 executed_transaction_count = 5;
  uint64 starting_transaction_index = 6; // added in v1.18, for solana 1.17 value is always 0
}

message SubscribeUpdatePing {}

message SubscribeUpdatePong {
  int32 id = 1;
}

// non-streaming methods

message PingRequest {
  int32 count = 1;
}

message PongResponse {
  int32 count = 1;
}

message GetLatestBlockhashRequest {
  optional CommitmentLevel commitment = 1;
}

message GetLatestBlockhashResponse {
  uint64 slot = 1;
  string blockhash = 2;
  uint64 last_valid_block_height = 3;
}

message GetBlockHeightRequest {
  optional CommitmentLevel commitment = 1;
}

message GetBlockHeightResponse {
  uint64 block_height = 1;
}

message GetSlotRequest {
  optional CommitmentLevel commitment = 1;
}

message GetSlotResponse {
  uint64 slot = 1;
}

message GetVersionRequest {}

message GetVersionResponse {
  string version = 1;
}

message IsBlockhashValidRequest {
  string blockhash = 1;
  optional CommitmentLevel commitment = 2;
}

message IsBlockhashValidResponse {
  uint64 slot = 1;
  bool valid = 2;
}