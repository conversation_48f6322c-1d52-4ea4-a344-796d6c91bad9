package model

import (
	"math/big"
	"time"
)

// ParseConfig 解析配置选项
type ParseConfig struct {
	TryUnknownDEX     bool     `json:"try_unknown_dex"`     // 是否尝试解析未知DEX
	ProgramIds        []string `json:"program_ids"`         // 只解析指定的程序ID
	IgnoreProgramIds  []string `json:"ignore_program_ids"`  // 忽略指定的程序ID
	ThrowError        bool     `json:"throw_error"`         // 解析失败时是否抛出错误
	AggregateTrades   bool     `json:"aggregate_trades"`    // 是否聚合交易记录
}

// DexInfo DEX协议信息
type DexInfo struct {
	ProgramId string `json:"program_id"` // DEX程序ID
	AMM       string `json:"amm"`        // 自动做市商名称
	Route     string `json:"route"`      // 路由器或聚合器名称
}

// TokenAmount 代币金额信息
type TokenAmount struct {
	Amount         string  `json:"amount"`           // 原始代币金额
	Decimals       int     `json:"decimals"`         // 代币精度
	UIAmount       float64 `json:"ui_amount"`        // UI显示金额
	UIAmountString string  `json:"ui_amount_string"` // UI金额字符串
}

// TokenInfo 代币信息
type TokenInfo struct {
	Mint                   string       `json:"mint"`                      // 代币mint地址
	Amount                 float64      `json:"amount"`                    // 代币UI金额
	AmountRaw              string       `json:"amount_raw"`                // 原始代币金额
	Decimals               int          `json:"decimals"`                  // 代币精度
	Authority              string       `json:"authority,omitempty"`       // 代币权限
	Destination            string       `json:"destination,omitempty"`     // 目标代币账户
	DestinationOwner       string       `json:"destination_owner,omitempty"` // 目标账户所有者
	DestinationBalance     *TokenAmount `json:"destination_balance,omitempty"`     // 转账后余额
	DestinationPreBalance  *TokenAmount `json:"destination_pre_balance,omitempty"` // 转账前余额
	Source                 string       `json:"source,omitempty"`          // 源代币账户
	SourceBalance          *TokenAmount `json:"source_balance,omitempty"`          // 源账户转账后余额
	SourcePreBalance       *TokenAmount `json:"source_pre_balance,omitempty"`      // 源账户转账前余额
	BalanceChange          string       `json:"balance_change,omitempty"`  // 用户余额变化
}

// TradeType 交易类型
type TradeType string

const (
	TradeTypeBuy  TradeType = "BUY"
	TradeTypeSell TradeType = "SELL"
)

// TradeInfo 交易信息
type TradeInfo struct {
	Type         TradeType  `json:"type"`          // 交易类型 (BUY/SELL)
	InputToken   *TokenInfo `json:"input_token"`   // 输入代币信息
	OutputToken  *TokenInfo `json:"output_token"`  // 输出代币信息
	User         string     `json:"user"`          // 用户地址
	ProgramId    string     `json:"program_id"`    // 程序ID
	AMM          string     `json:"amm"`           // AMM名称
	Route        string     `json:"route"`         // 路由名称
	Slot         uint64     `json:"slot"`          // 区块槽位
	Timestamp    int64      `json:"timestamp"`     // 时间戳
	Signature    string     `json:"signature"`     // 交易签名
	Index        string     `json:"idx"`           // 交易索引
}

// PoolEventType 流动性池事件类型
type PoolEventType string

const (
	PoolEventCreate   PoolEventType = "CREATE"
	PoolEventAdd      PoolEventType = "ADD"
	PoolEventRemove   PoolEventType = "REMOVE"
	PoolEventTrade    PoolEventType = "TRADE"
	PoolEventComplete PoolEventType = "COMPLETE"
)

// PoolEvent 流动性池事件
type PoolEvent struct {
	Type      PoolEventType `json:"type"`       // 事件类型
	User      string        `json:"user"`       // 用户地址
	ProgramId string        `json:"program_id"` // 程序ID
	AMM       string        `json:"amm"`        // AMM名称
	Slot      uint64        `json:"slot"`       // 区块槽位
	Timestamp int64         `json:"timestamp"`  // 时间戳
	Signature string        `json:"signature"`  // 交易签名
	Index     string        `json:"idx"`        // 事件索引
	
	// 池相关信息
	PoolId       string  `json:"pool_id,omitempty"`        // 池ID
	PoolLpMint   string  `json:"pool_lp_mint,omitempty"`   // LP代币mint
	Token0Mint   string  `json:"token0_mint,omitempty"`    // 代币0 mint
	Token1Mint   string  `json:"token1_mint,omitempty"`    // 代币1 mint
	Token0Amount float64 `json:"token0_amount,omitempty"`  // 代币0数量
	Token1Amount float64 `json:"token1_amount,omitempty"`  // 代币1数量
	
	// 扩展数据
	Data interface{} `json:"data,omitempty"` // 特定协议的额外数据
}

// TransferData 转账数据
type TransferData struct {
	Type             string     `json:"type"`               // 转账类型
	Source           string     `json:"source"`             // 源账户
	Destination      string     `json:"destination"`        // 目标账户
	Authority        string     `json:"authority"`          // 权限账户
	Amount           *big.Int   `json:"amount"`             // 转账金额
	Mint             string     `json:"mint"`               // 代币mint
	Decimals         int        `json:"decimals"`           // 代币精度
	UIAmount         float64    `json:"ui_amount"`          // UI金额
	ProgramId        string     `json:"program_id"`         // 程序ID
	InstructionIndex int        `json:"instruction_index"`  // 指令索引
	InnerIndex       *int       `json:"inner_index,omitempty"` // 内部指令索引
}

// BalanceChange 余额变化
type BalanceChange struct {
	Pre    *TokenAmount `json:"pre"`    // 变化前余额
	Post   *TokenAmount `json:"post"`   // 变化后余额
	Change *TokenAmount `json:"change"` // 变化金额
}

// ParseResult 解析结果
type ParseResult struct {
	State               bool                        `json:"state"`                 // 解析状态
	Fee                 *TokenAmount                `json:"fee"`                   // 交易手续费
	Trades              []TradeInfo                 `json:"trades"`                // 交易信息
	Liquidities         []PoolEvent                 `json:"liquidities"`           // 流动性事件
	Transfers           []TransferData              `json:"transfers"`             // 转账数据
	SOLBalanceChange    *BalanceChange              `json:"sol_balance_change,omitempty"`    // SOL余额变化
	TokenBalanceChange  map[string]*BalanceChange   `json:"token_balance_change,omitempty"`  // 代币余额变化
	MoreEvents          map[string][]interface{}    `json:"more_events,omitempty"`           // 其他事件
	Message             string                      `json:"msg,omitempty"`         // 错误消息
}

// ClassifiedInstruction 分类后的指令
type ClassifiedInstruction struct {
	Instruction interface{} `json:"instruction"`   // 指令数据
	ProgramId   string      `json:"program_id"`    // 程序ID
	OuterIndex  int         `json:"outer_index"`   // 外部指令索引
	InnerIndex  *int        `json:"inner_index,omitempty"` // 内部指令索引
}

// SolanaTransaction Solana交易接口
type SolanaTransaction interface {
	GetSlot() uint64
	GetBlockTime() *int64
	GetSignature() string
	GetVersion() *int
	GetMeta() interface{}
	GetTransaction() interface{}
}
